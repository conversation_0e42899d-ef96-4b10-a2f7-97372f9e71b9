# Run this with: poetry run python canjank.py
import can
import time
import signal
import sys

# Pause for 0.5 seconds at the beginning
time.sleep(0.5)

# Flag to indicate we're shutting down
running = True

# Signal handler for clean shutdown
def signal_handler(sig, frame):
    global running
    print("\nCaught signal, shutting down...")
    running = False

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# Configure with CAN-FD support
bus = None # Initialize bus to None
try:
    # Set up CAN with auto-retransmit disabled (if supported by the driver)
    bus = can.interface.Bus(
        channel='can3',
        interface='socketcan',
        fd=True,  # Enable CAN-FD support
        bitrate=1000000,
        data_bitrate=5000000,
        # Add timing parameters based on Moteus docs for 80MHz clock - https://github.com/otaviogood/moteus/blob/main/docs/reference.md
        sjw=10,
        dsjw=5,
        sample_point=0.666,
        dsample_point=0.666,
        # Attempt to disable automatic retransmission - not all interfaces support this
        # can_filters=None,
        receive_own_messages=False
    )
    
    # Try to disable auto-retransmission if the bus supports it
    try:
        if hasattr(bus, 'set_auto_retransmit'):
            bus.set_auto_retransmit(False)
    except:
        print("Note: Could not disable auto-retransmission")

    # Clear the receive buffer of any stale messages
    print("Clearing RX buffer...")
    while bus.recv(timeout=0) is not None:
        pass
    print("RX buffer cleared.")

    # Send a request to read the motor temperature from moteus
    print("Requesting motor temperature...")
    
    # Create a message to read the motor temperature register (0x00a)
    # Format: 0x80XX where XX is the device ID (using 1 as default)
    # Using subframe 0x14 to read an int16 register
    # Note: The high bit in the ID (0x80) indicates we want a reply
    device_id = 2
    source_id = 0
    broadcast = False
    msg = can.Message(
        arbitration_id=(source_id << 8) | (device_id if not broadcast else 0) | 0x8000,  # 0x8001 for device 1 with reply request
        data=[
            0x15,  # Read int16 registers, 1 of them.
            0x0e   # Start at register 0x00a (board temperature)
            # 0x14,  # Read int16 registers
            # 0x01,  # Read 1 register
            # 0x0e   # Start at register 0x00a (board temperature)
        ],
        is_extended_id=True, # since we |0x8000, it must be extended
        is_fd=True,  # This is a CAN-FD frame
        bitrate_switch=True  # Use the faster data bitrate (5 Mbps)
    )
    bus.send(msg)
    print("Temperature request sent.")

    # Receive messages (including CAN-FD)
    print("Waiting for responses...")
    
    # Counter for responses
    response_count = 0
    max_responses = 10
    
    while running and response_count < max_responses:
        msg = bus.recv(timeout=0.1)  # Shorter timeout to respond to signals faster
        if msg:
            print(f"Received - ID: {msg.arbitration_id:X}, FD: {msg.is_fd}, DLC: {msg.dlc}, Data: {bytes(msg.data).hex()}")
            
            # Count any message as a response
            response_count += 1
            print(f"Response {response_count}/{max_responses}")
            
            # Still parse temperature data if available
            # if msg.arbitration_id == ((device_id << 8) | source_id):
            if len(msg.data) >= 4 and (msg.data[0] & 0xfc) == 0x24:  # 0x24 is the reply marker for int16
                # data[3:5] should contain the temperature value in int16 format
                temp_raw = (msg.data[3] << 8) | msg.data[2]  # Little endian format
                # For int16, 1 LSB = 0.1C
                temp_celsius = temp_raw * 0.1
                print(f"{(msg.arbitration_id >> 8):X}: Motor Temperature: {temp_celsius:.1f}°C")
        else:
            print(".", end="", flush=True)  # Print dot for timeout

    print(f"\nReceived {response_count} responses. Exiting.")

except Exception as e:
    print(f"\nError: {e}")

finally:
    # Clean shutdown
    if bus:
        print("\nShutting down bus...")
        try:
            # Try to cancel all pending transmissions if possible
            if hasattr(bus, 'flush_tx_buffer'):
                bus.flush_tx_buffer()
        except:
            pass
            
        # Shutdown the bus interface
        bus.shutdown()
        print("Bus shut down.")
        
        # Additional steps to ensure clean shutdown
        try:
            # For SocketCAN specifically, try to bring down the interface
            if bus.channel == 'can3' and bus.interface == 'socketcan':
                import os
                print("Bringing down CAN interface via system command...")
                os.system("sudo ip link set can3 down")
                print("CAN interface down.")
        except:
            pass

print("Program terminated cleanly.")