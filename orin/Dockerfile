# Build image with: docker build -t l4t-image .
# run image with: docker run -it --rm --runtime nvidia --network=host -v $(pwd):/workspace l4t-image

# Base image for L4T r36.2.0 (JetPack 6.2)
ARG BASE_IMAGE=nvcr.io/nvidia/l4t-base:r36.2.0
FROM ${BASE_IMAGE}

# Set environment variables to avoid interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install common development tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    vim \
    python3 \
    python3-pip \
    python3-venv \
    iproute2 \
    can-utils \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Poetry in a virtual environment for better version control
RUN python3 -m venv /opt/poetry-venv \
    && /opt/poetry-venv/bin/pip install -U pip setuptools \
    && /opt/poetry-venv/bin/pip install poetry

# Add Poetry to PATH
ENV PATH="/opt/poetry-venv/bin:$PATH"

# Set the working directory
WORKDIR /workspace

# Default command (optional, can be overridden)
CMD ["/bin/bash"]
