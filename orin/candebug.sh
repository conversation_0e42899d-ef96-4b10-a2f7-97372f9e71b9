#!/bin/bash

echo "Bringing down CAN interface..."
sudo ip link set can3 down

echo "Reconfiguring CAN-FD parameters..."
# Use parameters from Moteus docs for 80MHz clocks - https://github.com/otaviogood/moteus/blob/main/docs/reference.md
sudo ip link set can3 type can bitrate 1000000 sjw 10 sample-point 0.666 dbitrate 5000000 dsjw 5 dsample-point 0.666 fd on restart-ms 1000

echo "Bringing up CAN interface..."
sudo ip link set can3 up

echo "Checking CAN interface status:"
ip -details link show can3
ip -s link show can3

echo "Running temperature monitoring program..."
poetry run python candebug.py

echo "Cleaning up CAN interface after program exit..."
sudo ip link set can3 down
echo "CAN interface down." 